import { isGelAvoirQuestion, isRegisterQuestion, QuestionsProps } from '@mynotary/frontend/shared/forms-util';
import React, { ReactElement } from 'react';
import { useSelector } from 'react-redux';
import { Questions } from '@mynotary/frontend/legals/api';
import { selectFormsMode } from '@mynotary/frontend/legals/api';
import { GelAvoirQuestion } from '@mynotary/frontend/gel-avoirs/api';
import { MnRegisterQuestionV2 } from '@mynotary/frontend/registers/api';
import { useWhitelist } from '@mynotary/frontend/whitelist/api';
import { FeatureWhiteListed } from '@mynotary/crossplatform/feature-flags/api';

export const RedactionQuestions = (props: QuestionsProps): ReactElement => {
  const formTypeMode = useSelector(selectFormsMode);
  const isDefaultQuestion = formTypeMode === 'DEFAULT_QUESTIONS';
  const isFormQuestionDisabled = !props.editable || !!props.answer?.locked;
  const isNewRegisterEntriesRouteEnabled = useWhitelist(FeatureWhiteListed.NEW_REGISTER_ENTRIES_ROUTE);
  console.log('isNewRegisterEntriesRouteEnabled', isNewRegisterEntriesRouteEnabled);

  return (
    <>
      <Questions {...props} />
      {isRegisterQuestion(props.question) &&
        // MyNotary Register Workflow's question
        (isNewRegisterEntriesRouteEnabled ? (
          <MnRegisterQuestionV2
            answer={props.answer}
            debounce={props.debounce}
            disabled={isFormQuestionDisabled || isDefaultQuestion}
            onChange={props.onChange}
            question={props.question}
          />
        ) : (
          // TODO : ATTENTION JUSTE POUR TESTS
          <MnRegisterQuestionV2
            answer={props.answer}
            debounce={props.debounce}
            disabled={isFormQuestionDisabled || isDefaultQuestion}
            onChange={props.onChange}
            question={props.question}
          />
        ))}
      {isGelAvoirQuestion(props.question) && (
        <GelAvoirQuestion
          answerValue={props.answer?.value}
          className={props.className}
          disabled={isFormQuestionDisabled}
          onChange={props.onChange}
          question={props.question}
        />
      )}
    </>
  );
};
