import './register-question-common.scss';
import { useParams } from 'react-router-dom';
import React, { ReactElement, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { MnSvg, MnTooltip } from '@mynotary/frontend/shared/ui';
import { selectOperation, selectContract } from '@mynotary/frontend/legals/api';
import { classNames, MnProps } from '@mynotary/frontend/shared/util';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { RegisterAddEntryPopin } from '../add-register-entry-popin/add-register-entry-popin';
import { RegisterEntry } from '@mynotary/frontend/registers/core';
import { getRegisterEntries, selectContractRegisterDefaultAnswer } from '@mynotary/frontend/registers/store';
import { Answer, RegisterFormQuestion } from '@mynotary/crossplatform/shared/forms-util';
import { selectCurrentOrganization } from '@mynotary/frontend/organizations/api';
import { setErrorMessage } from '@mynotary/frontend/snackbars/api';
import { RegisterQuestionInput } from './register-question-input';
import { RegisterQuestionConfirmationPoppin } from './register-question-confirmation-poppin';

interface RegisterQuestionCommonProps extends MnProps {
  answer: Answer;
  debounce: boolean;
  hasRegisterEntryManuallyCreatePermission: boolean;
  hasRegisterEntryReadPermission: boolean;
  onChange?: (value: string | null) => void;
  question: RegisterFormQuestion;
}

export const RegisterQuestionCommon = ({
  answer,
  debounce,
  hasRegisterEntryManuallyCreatePermission,
  hasRegisterEntryReadPermission,
  onChange,
  question
}: RegisterQuestionCommonProps): ReactElement => {
  const dispatch = useAsyncDispatch();
  const { contractId, id } = useParams<{ contractId: string; id: string }>();
  const organization = useSelector(selectCurrentOrganization);
  const operationId = parseInt(id ?? '');
  const operation = useSelector(selectOperation(operationId));
  const contract = useSelector(selectContract(parseInt(contractId ?? '')));
  const contractAnswer = useSelector(
    selectContractRegisterDefaultAnswer(question.register?.type, operation?.id, contract?.id)
  );

  const [entry, setEntry] = useState<RegisterEntry>();
  const [openConfirmationPopin, setOpenConfirmationPopin] = useState(false);
  const [isAdding, setIsAdding] = useState(false);
  const [isOnFocusInput, setIsOnFocusInput] = useState(false);

  const isRegisterNumberAvailable = !entry || entry.status === 'CLOSED';

  useEffect(() => {
    if (!hasRegisterEntryReadPermission || answer?.value == null || organization == null) {
      return;
    }
    const getRegisterEntry = async () => {
      const register = await dispatch(
        getRegisterEntries(
          question.register.type,
          `"numero_registre": {"value": ${answer.value}}`,
          0,
          1,
          organization.id
        )
      );
      setEntry(register?.entries?.[0]);
    };

    getRegisterEntry();
  }, [answer?.value, hasRegisterEntryReadPermission, dispatch, organization, question.register.type]);

  useEffect(() => {
    if (entry == null || !isOnFocusInput || entry?.operationId === operationId) {
      return;
    }
    setOpenConfirmationPopin(true);
  }, [entry, answer?.value, isOnFocusInput, operationId]);

  const handleRegisterFinished = (entry?: RegisterEntry): void => {
    setIsAdding(false);
    setEntry(entry);
    onChange?.(entry?.answer['numero_registre'].value);
  };

  const handleError = async () => {
    dispatch(setErrorMessage('Une erreur est survenue lors de la prise de numéro. Vouz pouvez annuler et réessayer.'));
    setIsAdding(false);
  };

  const handleAddRegistry = (): void => {
    if (!isRegisterNumberAvailable) return;
    setIsAdding(true);
  };

  return (
    <div className={classNames('mn-register-question')}>
      [{answer?.value}]
      <RegisterQuestionInput
        answer={answer}
        debounce={debounce}
        disabled={!hasRegisterEntryManuallyCreatePermission || !isRegisterNumberAvailable}
        hasRegisterEntryReadPermission={hasRegisterEntryReadPermission}
        onFocus={() => setIsOnFocusInput(true)}
        question={question}
      />
      <MnTooltip content={'Vous ne pouvez pas reprendre un numéro sans clore le précédent'}>
        <div className='rd-add' onClick={handleAddRegistry}>
          <MnSvg
            className={classNames('rq-add-register-entry-tooltip', { disabled: !isRegisterNumberAvailable })}
            path='/assets/images/pictos/icon/info-light.svg'
            variant={isRegisterNumberAvailable ? 'primary' : 'gray500-primary'}
          />
          <div className={classNames('rq-add-register-entry', { disabled: !isRegisterNumberAvailable })}>
            Prendre un numéro
          </div>
        </div>
      </MnTooltip>
      {contractAnswer && (
        <RegisterAddEntryPopin
          contractId={contract.id}
          defaultAnswer={contractAnswer}
          mode={question.register.type}
          onClose={() => setIsAdding(false)}
          onError={handleError}
          onFinished={handleRegisterFinished}
          opened={isAdding}
        />
      )}
      <RegisterQuestionConfirmationPoppin
        answer={answer}
        onChange={onChange}
        onClose={() => setOpenConfirmationPopin(false)}
        onCloseFocus={() => setIsOnFocusInput(false)}
        onResetEntry={() => setEntry(undefined)}
        openConfirmationPopin={openConfirmationPopin}
      />
    </div>
  );
};
