import './register-question-common.scss';
import React, { ReactElement } from 'react';
import { MnConfirmationPopin } from '@mynotary/frontend/shared/ui';
import { MnProps } from '@mynotary/frontend/shared/util';
import { Answer } from '@mynotary/crossplatform/shared/forms-util';

interface RegisterQuestionConfirmationPoppinProps extends MnProps {
  answer: Answer;
  onChange?: (value: string | null) => void;
  onClose?: () => void;
  onCloseFocus?: () => void;
  onResetEntry?: () => void;
  openConfirmationPopin: boolean;
}

export const RegisterQuestionConfirmationPoppin = ({
  answer,
  onChange,
  onClose,
  onCloseFocus,
  onResetEntry,
  openConfirmationPopin
}: RegisterQuestionConfirmationPoppinProps): ReactElement => {
  const handleCancelPopin = (): void => {
    onClose?.();
    onResetEntry?.();
    onCloseFocus?.();
    onChange?.(null);
  };

  return (
    <MnConfirmationPopin
      cancel='Non'
      clickOutsideToClose={false}
      content={`Le numéro ${answer?.value} est indiqué comme réservé dans le registre. Êtes-vous sûr de vouloir l'utiliser ?`}
      onCancel={handleCancelPopin}
      onValidate={() => onClose?.()}
      opened={openConfirmationPopin}
      validate='Oui'
    />
  );
};
