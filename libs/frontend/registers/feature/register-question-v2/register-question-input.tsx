import './register-question-common.scss';
import React, { ReactElement } from 'react';
import { MnInputText } from '@mynotary/frontend/shared/ui';
import { MnProps } from '@mynotary/frontend/shared/util';
import { Answer, RegisterFormQuestion } from '@mynotary/crossplatform/shared/forms-util';

interface RegisterCommonAddActionDisabledProps extends MnProps {
  answer: Answer;
  debounce: boolean;
  disabled: boolean;
  hasRegisterEntryReadPermission: boolean;
  onChange?: (value: string | null) => void;
  onFocus?: () => void;
  question: RegisterFormQuestion;
}

export const RegisterQuestionInput = ({
  answer,
  className,
  debounce,
  disabled,
  onChange,
  onFocus,
  question
}: RegisterCommonAddActionDisabledProps): ReactElement => {
  return (
    <MnInputText
      className={className}
      debounceTime={debounce ? 500 : 0}
      defaultValue={question.default}
      disabled={disabled}
      format={question.uppercase}
      onChange={onChange}
      onFocus={onFocus}
      placeholder={question.placeholder}
      required={!question.optional}
      value={answer?.value}
    />
  );
};
