import React, { ReactElement } from 'react';
import { useSelector } from 'react-redux';
import { hasPermission } from '@mynotary/frontend/roles/api';
import { selectConnectedUserRole } from '@mynotary/frontend/roles/api';
import { EntityType, PermissionType } from '@mynotary/crossplatform/roles/api';
import { useFeatureState } from '@mynotary/frontend/features/api';
import { FeatureType } from '@mynotary/crossplatform/features/api';
import { selectCurrentManagementRegister } from '@mynotary/frontend/registers/store';
import { MnProps } from '@mynotary/frontend/shared/util';
import { Answer, RegisterFormQuestion } from '@mynotary/crossplatform/shared/forms-util';
import { RegisterQuestionCommon } from './register-question-common';
import { RegisterCommonAddActionDisabled } from './register-question-common-add-action-disabled';
import { some } from 'lodash';
import { selectContract } from '@mynotary/frontend/legals/api';

interface RegisterManagementQuestionProps extends MnProps {
  answer: Answer;
  contractId?: string;
  debounce: boolean;
  disabled: boolean;
  onChange?: (value: string | null) => void;
  question: RegisterFormQuestion;
}

export const RegisterManagementQuestion = (props: RegisterManagementQuestionProps): ReactElement => {
  const role = useSelector(selectConnectedUserRole);
  const contract = useSelector(selectContract(parseInt(props.contractId ?? '')));
  const managementRegister = useSelector(selectCurrentManagementRegister);
  const { isActive: hasManagementFeature } = useFeatureState(FeatureType.MANAGEMENT_REGISTER_ACCESS);
  const isAllowedContract = some(
    props.question.register.contracts,
    (contractType) => contractType === contract?.legalContractTemplateId
  );
  const isRegisterInitialized = managementRegister?.config != null;

  const hasRegisterEntryCreatePermission = hasPermission(
    PermissionType.CREATE_ORGANIZATION_REGISTER_ENTRY,
    role,
    EntityType.MANAGEMENT_REGISTER
  );

  const hasRegisterEntryReadPermission = hasPermission(
    PermissionType.READ_ORGANIZATION_REGISTER,
    role,
    EntityType.MANAGEMENT_REGISTER
  );

  const hasRegisterEntryManuallyCreatePermission =
    !isRegisterInitialized ||
    hasPermission(PermissionType.CREATE_ORGANIZATION_REGISTER_MANUALLY_ENTRY, role, EntityType.MANAGEMENT_REGISTER);

  if (hasManagementFeature && hasRegisterEntryCreatePermission && isAllowedContract && isRegisterInitialized) {
    return (
      <RegisterQuestionCommon
        {...props}
        hasRegisterEntryManuallyCreatePermission={hasRegisterEntryManuallyCreatePermission}
        hasRegisterEntryReadPermission={hasRegisterEntryReadPermission}
      />
    );
  } else {
    return (
      <RegisterCommonAddActionDisabled
        {...props}
        hasRegisterEntryCreatePermission={hasRegisterEntryCreatePermission}
        hasRegisterEntryManuallyCreatePermission={hasRegisterEntryManuallyCreatePermission}
        hasRegisterEntryReadPermission={hasRegisterEntryReadPermission}
        isAllowedContract={isAllowedContract}
        isRegisterInitialized={isRegisterInitialized}
      />
    );
  }
};
