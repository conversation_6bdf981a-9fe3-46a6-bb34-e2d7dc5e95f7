import './register-question-common.scss';
import { useParams } from 'react-router-dom';
import React, { ReactElement, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { MnPopin, MnSvg, MnTooltip } from '@mynotary/frontend/shared/ui';
import { classNames, MnProps } from '@mynotary/frontend/shared/util';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { RegisterPopin } from '../register-popin/register-popin';
import { RegisterEntry } from '@mynotary/frontend/registers/core';
import { getRegisterEntries } from '@mynotary/frontend/registers/store';
import { Answer, RegisterFormQuestion } from '@mynotary/crossplatform/shared/forms-util';
import { selectCurrentOrganization } from '@mynotary/frontend/organizations/api';
import { RegisterQuestionInput } from './register-question-input';
import { RegisterQuestionConfirmationPoppin } from './register-question-confirmation-poppin';

interface RegisterCommonAddActionDisabledProps extends MnProps {
  answer: Answer;
  debounce: boolean;
  disabled: boolean;
  hasRegisterEntryCreatePermission: boolean;
  hasRegisterEntryManuallyCreatePermission: boolean;
  hasRegisterEntryReadPermission: boolean;
  isAllowedContract: boolean;
  isRegisterInitialized: boolean;
  onChange?: (value: string | null) => void;
  question: RegisterFormQuestion;
}

export const RegisterCommonAddActionDisabled = ({
  answer,
  debounce,
  disabled,
  hasRegisterEntryCreatePermission,
  hasRegisterEntryManuallyCreatePermission,
  hasRegisterEntryReadPermission,
  isAllowedContract,
  isRegisterInitialized,
  onChange,
  question
}: RegisterCommonAddActionDisabledProps): ReactElement => {
  const dispatch = useAsyncDispatch();
  const { id } = useParams<{ id: string }>();
  const organization = useSelector(selectCurrentOrganization);
  const operationId = parseInt(id ?? '');

  // registre entry : peut être modifié depuis RegisterQuestionConfirmationPoppin et RegisterQuestionInput
  const [entry, setEntry] = useState<RegisterEntry>();
  // gestion de la poppin de confirmation (RegisterQuestionConfirmationPoppin)
  const [openConfirmationPopin, setOpenConfirmationPopin] = useState(false);
  // gestion del a poppin d'action (inside this component)
  const [callToActionPopin, setCallToActionPopin] = useState(false);
  // gestion du focus sur l'input (RegiserQuestionInput)
  const [isOnFocusInput, setIsOnFocusInput] = useState(false);

  const isRegisterNumberAvailable = !entry || entry.status === 'CLOSED';

  useEffect(() => {
    console.log('SHERLOCK USE EFFECT', answer?.value, hasRegisterEntryReadPermission, organization);
    if (!hasRegisterEntryReadPermission || answer?.value == null || organization == null) {
      return;
    }
    const getRegisterEntry = async () => {
      const register = await dispatch(
        getRegisterEntries(
          question.register.type,
          `"numero_registre": {"value": ${answer.value}}`,
          0,
          1,
          organization.id
        )
      );
      setEntry(register?.entries?.[0]);
    };

    getRegisterEntry();
  }, [answer?.value, hasRegisterEntryReadPermission, dispatch, organization, question.register.type]);

  useEffect(() => {
    console.log('SHERLOCK USE EFFECT', entry, isOnFocusInput, operationId);
    if (entry == null || !isOnFocusInput || entry?.operationId === operationId) {
      return;
    }
    setOpenConfirmationPopin(true);
  }, [entry, answer?.value, isOnFocusInput, operationId]);
  const getTooltipContent = (
    disabled: boolean,
    hasRegisterEntryCreatePermission: boolean,
    isRegisterInitialized: boolean,
    isAllowedContract: boolean,
    isRegisterNumberAvailable: boolean
  ): string => {
    if (disabled) {
      return 'Vous ne pouvez pas prendre de numéro sur un contrat validé';
    } else if (!hasRegisterEntryCreatePermission) {
      return `Vous n'avez pas le droit de prendre un numéro de mandat`;
    } else if (!isRegisterInitialized) {
      return 'Vous ne pouvez pas prendre de numéro sans avoir initialisé le registre au préalable. Rendez-vous dans votre espace Paramètres > Registre (transaction ou gestion)';
    } else if (!isAllowedContract) {
      return 'Vous ne pouvez pas prendre de numéro dans ce contrat';
    } else if (!isRegisterNumberAvailable) {
      return 'Vous ne pouvez pas reprendre un numéro sans clore le précédent';
    }
    return '';
  };

  return (
    <div className={classNames('mn-register-question')}>
      <RegisterQuestionInput
        answer={answer}
        debounce={debounce}
        disabled={disabled || !hasRegisterEntryManuallyCreatePermission || !isRegisterNumberAvailable}
        hasRegisterEntryReadPermission={hasRegisterEntryReadPermission}
        onFocus={() => setIsOnFocusInput(true)}
        question={question}
      />

      <MnTooltip
        content={getTooltipContent(
          disabled,
          hasRegisterEntryCreatePermission,
          isRegisterInitialized,
          isAllowedContract,
          isRegisterNumberAvailable
        )}
      >
        <div className='rd-add' onClick={() => setCallToActionPopin(true)}>
          <MnSvg
            className={classNames('rq-add-register-entry-tooltip', { disabled: true })}
            path='/assets/images/pictos/icon/info-light.svg'
            variant='gray500-primary'
          />
          <div className={classNames('rq-add-register-entry', { disabled: true })}>Prendre un numéro</div>
        </div>
      </MnTooltip>

      <RegisterQuestionConfirmationPoppin
        answer={answer}
        onChange={onChange}
        onClose={() => setOpenConfirmationPopin(false)}
        onCloseFocus={() => setIsOnFocusInput(false)}
        onResetEntry={() => setEntry(undefined)}
        openConfirmationPopin={openConfirmationPopin}
      />

      <MnPopin
        className='register-question-popin'
        onClose={() => setCallToActionPopin(false)}
        opened={callToActionPopin}
      >
        <RegisterPopin />
      </MnPopin>
    </div>
  );
};
